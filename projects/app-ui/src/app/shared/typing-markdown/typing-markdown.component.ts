import {
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  Input,
  ViewEncapsulation,
  OnInit,
  OnDestroy,
  ChangeDetectorRef,
  inject
} from '@angular/core';
import { MarkdownPipe } from '../pipes/markdown.pipe';
import { AsyncPipe } from '@angular/common';
import { ChatMessageService } from '../../features/chat/_services/chat-message.service';
import { Subscription } from 'rxjs';

/**
 * 带打字效果的Markdown组件
 * 用于显示AI助手回复时的打字效果
 */
@Component({
  selector: 'app-typing-markdown',
  template: `<div class="markdown-content" [innerHTML]="displayContent | markdown | async"></div>`,
  styleUrls: ['../markdown/markdown.component.scss'],
  standalone: true,
  imports: [MarkdownPipe, AsyncPipe],
  encapsulation: ViewEncapsulation.None,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class TypingMarkdownComponent implements OnInit, OnDestroy {
  @Input() content = '';
  @Input() messageId = '';
  @Input() enableTyping = true;
  @Input() typingSpeed = 30; // 每个字符的显示间隔（毫秒）

  displayContent = '';
  private typingSubscription?: Subscription;
  private messageService = inject(ChatMessageService);
  private cdr = inject(ChangeDetectorRef);

  ngOnInit(): void {
    if (this.enableTyping && this.messageId) {
      this.startTypingEffect();
    } else {
      // 如果不启用打字效果，直接显示完整内容
      this.displayContent = this.content;
    }
  }

  ngOnDestroy(): void {
    this.stopTypingEffect();
  }

  /**
   * 启动打字效果
   */
  private startTypingEffect(): void {
    this.displayContent = '';
    
    // 订阅打字效果状态
    this.typingSubscription = this.messageService
      .startTypingEffect(this.messageId, this.typingSpeed)
      .subscribe({
        next: (state) => {
          this.displayContent = state.displayedText;
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('打字效果出错:', error);
          // 出错时显示完整内容
          this.displayContent = this.content;
          this.cdr.detectChanges();
        }
      });
  }

  /**
   * 停止打字效果
   */
  private stopTypingEffect(): void {
    if (this.typingSubscription) {
      this.typingSubscription.unsubscribe();
      this.typingSubscription = undefined;
    }
  }

  /**
   * 立即完成打字效果
   */
  completeTyping(): void {
    if (this.messageId) {
      this.messageService.completeTypingEffect(this.messageId);
    }
  }

  /**
   * 获取当前打字状态
   */
  getTypingState(): any {
    if (this.messageId) {
      return this.messageService.getTypingState(this.messageId);
    }
    return null;
  }
}
