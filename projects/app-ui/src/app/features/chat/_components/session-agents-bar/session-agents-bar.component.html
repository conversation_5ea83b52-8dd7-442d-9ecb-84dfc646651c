<div class="session-agents-bar">
  <!-- 空状态 -->
  <div *ngIf="!hasAgents()" class="empty-state">
    <button 
      mat-raised-button 
      color="primary"
      [matTooltip]="getAddButtonTooltip()"
      (click)="openAgentSelection()">
      <mat-icon>add</mat-icon>
      选择AI助手
    </button>
  </div>

  <!-- Agent列表 -->
  <div *ngIf="hasAgents()" class="agents-container">
    <!-- 显示的Agent -->
    <div class="agents-list">
      <div 
        *ngFor="let agent of getDisplayAgents()" 
        class="agent-item"
        [matTooltip]="getAgentTooltip(agent)"
        matTooltipPosition="below">
        
        <!-- Agent头像 -->
        <div class="agent-avatar">
          <img 
            *ngIf="agent.avatarUrl" 
            [src]="agent.avatarUrl" 
            [alt]="agent.name"
            class="avatar-image">
          <div 
            *ngIf="!agent.avatarUrl" 
            class="avatar-emoji">
            {{ getAgentAvatar(agent) }}
          </div>
          
          <!-- 移除按钮 -->
          <button 
            *ngIf="hasMultipleAgents()"
            mat-icon-button 
            class="remove-button"
            matTooltip="暂停此Agent"
            (click)="toggleAgent(agent, $event)">
            <mat-icon>close</mat-icon>
          </button>
        </div>
        
        <!-- Agent名称 -->
        <span class="agent-name">{{ agent.name }}</span>
      </div>
    </div>

    <!-- 更多Agent指示器 -->
    <div *ngIf="getHiddenAgentCount() > 0" class="more-agents">
      <div 
        class="more-indicator"
        [matTooltip]="'还有 ' + getHiddenAgentCount() + ' 个Agent'"
        matTooltipPosition="below">
        <mat-icon>more_horiz</mat-icon>
        <span>+{{ getHiddenAgentCount() }}</span>
      </div>
    </div>

    <!-- 管理按钮 -->
    <div class="manage-button">
      <button 
        mat-icon-button 
        [matTooltip]="getAddButtonTooltip()"
        (click)="openAgentSelection()">
        <mat-icon>settings</mat-icon>
      </button>
    </div>
  </div>

  <!-- 协作指示器 -->
  <div *ngIf="hasMultipleAgents()" class="collaboration-indicator">
    <mat-icon>group_work</mat-icon>
    <span>{{ sessionAgents.length }}个AI助手协作中</span>
  </div>
</div>
