.session-agents-bar {
  padding: 12px 16px;
  background-color: var(--md-sys-color-surface-container-low);
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
  min-height: 60px;
  display: flex;
  align-items: center;

  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;

    button {
      mat-icon {
        margin-right: 8px;
      }
    }
  }

  .agents-container {
    display: flex;
    align-items: center;
    width: 100%;
    gap: 12px;

    .agents-list {
      display: flex;
      align-items: center;
      gap: 12px;
      flex: 1;
      overflow-x: auto;
      scrollbar-width: none;
      -ms-overflow-style: none;

      &::-webkit-scrollbar {
        display: none;
      }

      .agent-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;
        min-width: 60px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          transform: scale(1.05);
        }

        .agent-avatar {
          position: relative;
          width: 40px;
          height: 40px;

          .avatar-image {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid var(--md-sys-color-primary);
          }

          .avatar-emoji {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background-color: var(--md-sys-color-primary-container);
            border: 2px solid var(--md-sys-color-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
          }

          .remove-button {
            position: absolute;
            top: -6px;
            right: -6px;
            width: 20px;
            height: 20px;
            min-height: 20px;
            background-color: var(--md-sys-color-error);
            color: var(--md-sys-color-on-error);
            opacity: 0;
            transition: opacity 0.2s ease;

            mat-icon {
              font-size: 14px;
              width: 14px;
              height: 14px;
            }
          }

          &:hover .remove-button {
            opacity: 1;
          }
        }

        .agent-name {
          font-size: 12px;
          color: var(--md-sys-color-on-surface);
          text-align: center;
          max-width: 60px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .more-agents {
      .more-indicator {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;
        min-width: 60px;
        cursor: pointer;
        color: var(--md-sys-color-on-surface-variant);

        mat-icon {
          width: 40px;
          height: 40px;
          font-size: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          background-color: var(--md-sys-color-surface-variant);
        }

        span {
          font-size: 12px;
        }
      }
    }

    .manage-button {
      button {
        color: var(--md-sys-color-on-surface-variant);
        
        &:hover {
          background-color: var(--md-sys-color-surface-variant);
        }
      }
    }
  }

  .collaboration-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    padding: 4px 8px;
    background-color: var(--md-sys-color-primary-container);
    color: var(--md-sys-color-on-primary-container);
    border-radius: 12px;
    font-size: 12px;

    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .session-agents-bar {
    padding: 8px 12px;
    min-height: 50px;

    .agents-container {
      .agents-list {
        gap: 8px;

        .agent-item {
          min-width: 50px;

          .agent-avatar {
            width: 32px;
            height: 32px;

            .avatar-emoji {
              font-size: 16px;
            }
          }

          .agent-name {
            font-size: 10px;
            max-width: 50px;
          }
        }
      }
    }

    .collaboration-indicator {
      font-size: 11px;
      padding: 2px 6px;

      mat-icon {
        font-size: 14px;
        width: 14px;
        height: 14px;
      }
    }
  }
}

// 动画效果
@keyframes agentAdded {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.agent-item {
  animation: agentAdded 0.3s ease-out;
}

// 暗色主题适配
@media (prefers-color-scheme: dark) {
  .session-agents-bar {
    border-bottom-color: var(--md-sys-color-outline);
  }
}
