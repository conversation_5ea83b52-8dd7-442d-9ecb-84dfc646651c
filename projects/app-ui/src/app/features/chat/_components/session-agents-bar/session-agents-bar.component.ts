import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Subject, takeUntil } from 'rxjs';
import { Agent } from '../../_services/execution-plan';
import { AgentService } from '../../_services/agent.service';
import { AgentSelectionDialogComponent } from '../agent-selection-dialog/agent-selection-dialog.component';
import { MatSnackBar } from '@angular/material/snack-bar';

/**
 * 会话Agent栏组件
 * 显示当前会话中的Agent，支持添加、移除和切换激活状态
 */
@Component({
  selector: 'app-session-agents-bar',
  templateUrl: './session-agents-bar.component.html',
  styleUrls: ['./session-agents-bar.component.scss']
})
export class SessionAgentsBarComponent implements OnInit, OnDestroy {
  @Input() sessionId!: string;
  @Output() agentsChanged = new EventEmitter<Agent[]>();

  sessionAgents: Agent[] = [];
  private destroy$ = new Subject<void>();

  constructor(
    private agentService: AgentService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initializeSessionAgents();
    this.subscribeToAgentChanges();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * 初始化会话Agent
   */
  private initializeSessionAgents(): void {
    if (this.sessionId) {
      this.agentService.initializeSessionData(this.sessionId);
    }
  }

  /**
   * 订阅Agent变化
   */
  private subscribeToAgentChanges(): void {
    this.agentService.sessionAgents$
      .pipe(takeUntil(this.destroy$))
      .subscribe(agents => {
        this.sessionAgents = agents;
        this.agentsChanged.emit(agents);
      });
  }

  /**
   * 打开Agent选择对话框
   */
  openAgentSelection(): void {
    const dialogRef = this.dialog.open(AgentSelectionDialogComponent, {
      width: '800px',
      maxWidth: '90vw',
      data: {
        sessionId: this.sessionId,
        title: '管理会话Agent',
        subtitle: '选择要在此会话中使用的AI助手',
        allowMultiple: true,
        preselectedAgents: this.sessionAgents
      }
    });

    dialogRef.afterClosed().subscribe(selectedAgents => {
      if (selectedAgents) {
        this.updateSessionAgents(selectedAgents);
      }
    });
  }

  /**
   * 更新会话Agent
   */
  private updateSessionAgents(selectedAgents: Agent[]): void {
    const currentAgentIds = new Set(this.sessionAgents.map(a => a.id));
    const selectedAgentIds = new Set(selectedAgents.map(a => a.id));

    // 添加新选择的Agent
    selectedAgents.forEach(agent => {
      if (!currentAgentIds.has(agent.id)) {
        this.agentService.addAgentToSession(this.sessionId, agent.id).subscribe({
          next: () => {
            this.snackBar.open(`已添加 ${agent.name}`, '关闭', { duration: 2000 });
          },
          error: (error) => {
            console.error('添加Agent失败:', error);
            this.snackBar.open(`添加 ${agent.name} 失败`, '关闭', { duration: 3000 });
          }
        });
      }
    });

    // 移除未选择的Agent
    this.sessionAgents.forEach(agent => {
      if (!selectedAgentIds.has(agent.id)) {
        this.agentService.removeAgentFromSession(this.sessionId, agent.id).subscribe({
          next: () => {
            this.snackBar.open(`已移除 ${agent.name}`, '关闭', { duration: 2000 });
          },
          error: (error) => {
            console.error('移除Agent失败:', error);
            this.snackBar.open(`移除 ${agent.name} 失败`, '关闭', { duration: 3000 });
          }
        });
      }
    });
  }

  /**
   * 切换Agent激活状态
   */
  toggleAgent(agent: Agent, event: Event): void {
    event.stopPropagation();
    
    // 如果只有一个Agent，不允许取消激活
    if (this.sessionAgents.length === 1) {
      this.snackBar.open('至少需要保留一个Agent', '关闭', { duration: 2000 });
      return;
    }

    this.agentService.removeAgentFromSession(this.sessionId, agent.id).subscribe({
      next: () => {
        this.snackBar.open(`已暂停 ${agent.name}`, '关闭', { duration: 2000 });
      },
      error: (error) => {
        console.error('切换Agent状态失败:', error);
        this.snackBar.open(`操作失败`, '关闭', { duration: 3000 });
      }
    });
  }

  /**
   * 获取Agent头像
   */
  getAgentAvatar(agent: Agent): string {
    return this.agentService.getAvatarUrl(agent);
  }

  /**
   * 获取显示的Agent数量
   */
  getDisplayAgentCount(): number {
    return Math.min(this.sessionAgents.length, 5); // 最多显示5个
  }

  /**
   * 获取隐藏的Agent数量
   */
  getHiddenAgentCount(): number {
    return Math.max(0, this.sessionAgents.length - 5);
  }

  /**
   * 获取显示的Agent列表
   */
  getDisplayAgents(): Agent[] {
    return this.sessionAgents.slice(0, 5);
  }

  /**
   * 获取隐藏的Agent列表
   */
  getHiddenAgents(): Agent[] {
    return this.sessionAgents.slice(5);
  }

  /**
   * 检查是否有Agent
   */
  hasAgents(): boolean {
    return this.sessionAgents.length > 0;
  }

  /**
   * 检查是否有多个Agent
   */
  hasMultipleAgents(): boolean {
    return this.sessionAgents.length > 1;
  }

  /**
   * 获取Agent工具提示
   */
  getAgentTooltip(agent: Agent): string {
    return `${agent.name} - ${agent.description || '点击管理'}`;
  }

  /**
   * 获取添加按钮工具提示
   */
  getAddButtonTooltip(): string {
    if (this.sessionAgents.length === 0) {
      return '添加AI助手';
    } else {
      return '管理AI助手';
    }
  }
}
