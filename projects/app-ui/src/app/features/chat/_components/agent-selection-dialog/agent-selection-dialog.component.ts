import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Agent } from '../../_services/execution-plan';
import { AgentService } from '../../_services/agent.service';
import { MatSnackBar } from '@angular/material/snack-bar';

export interface AgentSelectionDialogData {
  sessionId: string;
  title?: string;
  subtitle?: string;
  allowMultiple?: boolean;
  preselectedAgents?: Agent[];
}

/**
 * Agent选择对话框组件
 * 用于新建会话时选择Agent或在会话中管理Agent
 */
@Component({
  selector: 'app-agent-selection-dialog',
  templateUrl: './agent-selection-dialog.component.html',
  styleUrls: ['./agent-selection-dialog.component.scss']
})
export class AgentSelectionDialogComponent implements OnInit {
  availableAgents: Agent[] = [];
  selectedAgents: Set<string> = new Set();
  loading = true;
  subscribing = false;

  constructor(
    public dialogRef: MatDialogRef<AgentSelectionDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: AgentSelectionDialogData,
    private agentService: AgentService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadAvailableAgents();
    this.initializeSelectedAgents();
  }

  /**
   * 加载可用的Agent列表
   */
  private loadAvailableAgents(): void {
    this.loading = true;
    this.agentService.getAllAgents().subscribe({
      next: (agents) => {
        this.availableAgents = agents;
        this.loading = false;
      },
      error: (error) => {
        console.error('加载Agent列表失败:', error);
        this.snackBar.open('加载Agent列表失败', '关闭', { duration: 3000 });
        this.loading = false;
      }
    });
  }

  /**
   * 初始化已选择的Agent
   */
  private initializeSelectedAgents(): void {
    if (this.data.preselectedAgents) {
      this.data.preselectedAgents.forEach(agent => {
        this.selectedAgents.add(agent.id);
      });
    }
  }

  /**
   * 切换Agent选择状态
   */
  toggleAgent(agent: Agent): void {
    if (!this.agentService.isAgentAvailable(agent)) {
      // 如果Agent不可用，尝试订阅
      this.subscribeAgent(agent);
      return;
    }

    if (this.selectedAgents.has(agent.id)) {
      this.selectedAgents.delete(agent.id);
    } else {
      if (!this.data.allowMultiple) {
        // 单选模式，清除其他选择
        this.selectedAgents.clear();
      }
      this.selectedAgents.add(agent.id);
    }
  }

  /**
   * 订阅Agent
   */
  subscribeAgent(agent: Agent): void {
    if (this.subscribing) return;

    this.subscribing = true;
    this.agentService.subscribeAgent(agent.id).subscribe({
      next: () => {
        this.snackBar.open(`成功订阅 ${agent.name}`, '关闭', { duration: 3000 });
        // 更新Agent状态
        agent.isSubscribed = true;
        agent.subscriptionStatus = 'SUBSCRIBED';
        // 自动选择该Agent
        this.selectedAgents.add(agent.id);
        this.subscribing = false;
      },
      error: (error) => {
        console.error('订阅Agent失败:', error);
        this.snackBar.open(`订阅 ${agent.name} 失败`, '关闭', { duration: 3000 });
        this.subscribing = false;
      }
    });
  }

  /**
   * 检查Agent是否被选中
   */
  isAgentSelected(agent: Agent): boolean {
    return this.selectedAgents.has(agent.id);
  }

  /**
   * 获取选中的Agent列表
   */
  getSelectedAgents(): Agent[] {
    return this.availableAgents.filter(agent => this.selectedAgents.has(agent.id));
  }

  /**
   * 确认选择
   */
  confirm(): void {
    const selectedAgents = this.getSelectedAgents();
    if (selectedAgents.length === 0) {
      this.snackBar.open('请至少选择一个Agent', '关闭', { duration: 3000 });
      return;
    }

    this.dialogRef.close(selectedAgents);
  }

  /**
   * 取消选择
   */
  cancel(): void {
    this.dialogRef.close();
  }

  /**
   * 获取Agent头像
   */
  getAgentAvatar(agent: Agent): string {
    return this.agentService.getAvatarUrl(agent);
  }

  /**
   * 获取Agent价格文本
   */
  getAgentPrice(agent: Agent): string {
    return this.agentService.formatPrice(agent.monthlyPrice);
  }

  /**
   * 获取订阅状态文本
   */
  getSubscriptionStatusText(agent: Agent): string {
    return this.agentService.getSubscriptionStatusText(agent);
  }

  /**
   * 获取订阅状态颜色
   */
  getSubscriptionStatusColor(agent: Agent): string {
    return this.agentService.getSubscriptionStatusColor(agent);
  }

  /**
   * 检查是否可以确认
   */
  canConfirm(): boolean {
    return this.selectedAgents.size > 0 && !this.loading && !this.subscribing;
  }

  /**
   * 获取对话框标题
   */
  getDialogTitle(): string {
    return this.data.title || '选择AI助手';
  }

  /**
   * 获取对话框副标题
   */
  getDialogSubtitle(): string {
    return this.data.subtitle || '请选择一个或多个AI助手来协助您';
  }

  /**
   * 获取确认按钮文本
   */
  getConfirmButtonText(): string {
    const count = this.selectedAgents.size;
    if (count === 0) {
      return '确认选择';
    } else if (count === 1) {
      return '确认选择 (1个Agent)';
    } else {
      return `确认选择 (${count}个Agent)`;
    }
  }
}
