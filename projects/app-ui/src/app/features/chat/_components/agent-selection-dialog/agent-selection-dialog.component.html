<div class="agent-selection-dialog">
  <h2 mat-dialog-title>{{ getDialogTitle() }}</h2>
  
  <mat-dialog-content>
    <p class="dialog-subtitle">{{ getDialogSubtitle() }}</p>
    
    <!-- 加载状态 -->
    <div *ngIf="loading" class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>加载Agent列表...</p>
    </div>
    
    <!-- Agent列表 -->
    <div *ngIf="!loading" class="agents-grid">
      <mat-card 
        *ngFor="let agent of availableAgents" 
        class="agent-card"
        [class.selected]="isAgentSelected(agent)"
        [class.unavailable]="!agentService.isAgentAvailable(agent)"
        (click)="toggleAgent(agent)">
        
        <!-- Agent头像 -->
        <div class="agent-avatar">
          <img 
            *ngIf="agent.avatarUrl" 
            [src]="agent.avatarUrl" 
            [alt]="agent.name"
            class="avatar-image">
          <div 
            *ngIf="!agent.avatarUrl" 
            class="avatar-emoji">
            {{ getAgentAvatar(agent) }}
          </div>
          
          <!-- 选中状态指示器 -->
          <mat-icon 
            *ngIf="isAgentSelected(agent)" 
            class="selection-indicator">
            check_circle
          </mat-icon>
        </div>
        
        <!-- Agent信息 -->
        <mat-card-content class="agent-info">
          <h3 class="agent-name">{{ agent.name }}</h3>
          <p class="agent-description">{{ agent.description }}</p>
          
          <!-- 价格和状态 -->
          <div class="agent-status">
            <mat-chip 
              [color]="getSubscriptionStatusColor(agent)"
              class="price-chip">
              {{ getAgentPrice(agent) }}
            </mat-chip>
            
            <mat-chip 
              *ngIf="!agentService.isAgentAvailable(agent)"
              color="accent"
              class="status-chip">
              {{ getSubscriptionStatusText(agent) }}
            </mat-chip>
          </div>
        </mat-card-content>
        
        <!-- 订阅按钮 -->
        <mat-card-actions *ngIf="!agentService.isAgentAvailable(agent)" class="agent-actions">
          <button 
            mat-raised-button 
            color="primary"
            [disabled]="subscribing"
            (click)="subscribeAgent(agent); $event.stopPropagation()">
            <mat-icon *ngIf="subscribing">hourglass_empty</mat-icon>
            <mat-icon *ngIf="!subscribing">add_shopping_cart</mat-icon>
            {{ subscribing ? '订阅中...' : '立即订阅' }}
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
    
    <!-- 选择提示 -->
    <div *ngIf="!loading && selectedAgents.size === 0" class="selection-hint">
      <mat-icon>info</mat-icon>
      <p>请选择至少一个AI助手来开始对话</p>
    </div>
    
    <!-- 多选提示 -->
    <div *ngIf="!loading && data.allowMultiple && selectedAgents.size > 1" class="multi-selection-hint">
      <mat-icon>group</mat-icon>
      <p>已选择 {{ selectedAgents.size }} 个AI助手，它们将协作为您提供服务</p>
    </div>
  </mat-dialog-content>
  
  <mat-dialog-actions align="end">
    <button mat-button (click)="cancel()">取消</button>
    <button 
      mat-raised-button 
      color="primary"
      [disabled]="!canConfirm()"
      (click)="confirm()">
      {{ getConfirmButtonText() }}
    </button>
  </mat-dialog-actions>
</div>
