.agent-selection-dialog {
  min-width: 600px;
  max-width: 800px;
  max-height: 80vh;

  .dialog-subtitle {
    color: var(--md-sys-color-on-surface-variant);
    margin-bottom: 24px;
    font-size: 14px;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40px 20px;
    gap: 16px;

    p {
      color: var(--md-sys-color-on-surface-variant);
      margin: 0;
    }
  }

  .agents-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
  }

  .agent-card {
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
    position: relative;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &.selected {
      border-color: var(--md-sys-color-primary);
      background-color: var(--md-sys-color-primary-container);
    }

    &.unavailable {
      opacity: 0.7;
      
      &:hover {
        transform: none;
      }
    }

    .agent-avatar {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 80px;
      margin-bottom: 16px;

      .avatar-image {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        object-fit: cover;
      }

      .avatar-emoji {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        background-color: var(--md-sys-color-surface-variant);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32px;
      }

      .selection-indicator {
        position: absolute;
        top: 0;
        right: 0;
        color: var(--md-sys-color-primary);
        background-color: var(--md-sys-color-surface);
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
      }
    }

    .agent-info {
      text-align: center;
      padding: 0 16px 16px;

      .agent-name {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 500;
        color: var(--md-sys-color-on-surface);
      }

      .agent-description {
        margin: 0 0 16px 0;
        font-size: 14px;
        color: var(--md-sys-color-on-surface-variant);
        line-height: 1.4;
        min-height: 40px;
      }

      .agent-status {
        display: flex;
        justify-content: center;
        gap: 8px;
        flex-wrap: wrap;

        .price-chip,
        .status-chip {
          font-size: 12px;
          height: 24px;
          line-height: 24px;
        }
      }
    }

    .agent-actions {
      padding: 8px 16px 16px;
      justify-content: center;

      button {
        min-width: 120px;

        mat-icon {
          margin-right: 8px;
        }
      }
    }
  }

  .selection-hint,
  .multi-selection-hint {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background-color: var(--md-sys-color-surface-variant);
    border-radius: 8px;
    margin-top: 16px;

    mat-icon {
      color: var(--md-sys-color-on-surface-variant);
    }

    p {
      margin: 0;
      color: var(--md-sys-color-on-surface-variant);
      font-size: 14px;
    }
  }

  .multi-selection-hint {
    background-color: var(--md-sys-color-primary-container);

    mat-icon,
    p {
      color: var(--md-sys-color-on-primary-container);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .agent-selection-dialog {
    min-width: 90vw;
    max-width: 90vw;

    .agents-grid {
      grid-template-columns: 1fr;
    }
  }
}

// 暗色主题适配
@media (prefers-color-scheme: dark) {
  .agent-card {
    &:hover {
      box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
    }
  }
}
