<div class="message-container" [class.user-message]="isUser()" [class.assistant-message]="isAssistant()"
     [attr.data-message-id]="message.id">
  <div class="message-row">
    @if (isUser()) {
      <div class="avatar">
        <div class="user-avatar">
          <img src="/assets/user-avatar.svg" alt="用户" class="avatar-image">
        </div>
      </div>
      <div class="message-content">
        <div class="message-body" [class.generating]="message.isGenerating">
          <app-markdown [content]="message.content"></app-markdown>

          <!-- 附件列表 -->
          @if (message.attachments && message.attachments.length > 0) {
            <div class="message-attachments">
              @for (attachment of message.attachments; track attachment.id) {
                <a
                  class="attachment-item"
                  [href]="attachment.url"
                  target="_blank"
                  [matTooltip]="attachment.name"
                >
                  <div class="attachment-icon">
                    <!-- 根据文件类型显示不同图标 -->
                    @if (isImageFile(attachment.type)) {
                      <img [src]="attachment.url" alt="附件预览" class="attachment-preview">
                    } @else {
                      <div class="file-icon">
                        {{ getFileExtension(attachment.name).toUpperCase() }}
                      </div>
                    }
                  </div>
                  <div class="attachment-info">
                    <span class="attachment-name">{{ attachment.name }}</span>
                    <span class="attachment-size">{{ formatFileSize(attachment.size) }}</span>
                  </div>
                </a>
              }
            </div>
          }

          <!-- 用户消息操作按钮 -->
          <div class="message-actions">
            <button mat-icon-button matTooltip="复制" (click)="onCopy()">
              <mat-icon svgIcon="content_copy"></mat-icon>
            </button>
            @if (isUser()) {
              <button mat-icon-button matTooltip="编辑并重新发送" (click)="onEditAndResend()">
                <mat-icon svgIcon="edit"></mat-icon>
              </button>
              <button mat-icon-button matTooltip="删除" (click)="onDelete()">
                <mat-icon svgIcon="delete"></mat-icon>
              </button>
            }
          </div>
        </div>
      </div>
    } @else {
      <div class="avatar">
        <div class="assistant-avatar">
          <img src="/assets/assistant-avatar.svg" alt="AI助手" class="avatar-image">
        </div>
      </div>
      <div class="message-content">
        <!-- 生成状态组件，显示在助理消息上方 -->
        <app-chat-generation-status [message]="message"></app-chat-generation-status>

        <div class="message-header">
          <span class="message-sender">{{ message.assistantName ?? 'AI 助理' }}</span>

          <!-- 版本切换导航 -->
          @if (hasMultipleVersions() && replyToMessage) {
            <div class="version-navigation">
              <!-- 显示最多3个版本按钮 -->
              @if (replyToMessage.replyIds && replyToMessage.replyIds.length <= 3) {
                @for (replyId of replyToMessage.replyIds; track replyId; let i = $index) {
                  <button
                    class="version-button"
                    [class.active]="replyId === replyToMessage.activeReplyId"
                    (click)="onSwitchVersion(replyId)">
                    {{ i + 1 }}
                  </button>
                }
              } @else {
                <!-- 显示前2个版本按钮和下拉菜单 -->
                @for (replyId of getFirstNVersions(2); track replyId; let i = $index) {
                  <button
                    class="version-button"
                    [class.active]="replyId === replyToMessage.activeReplyId"
                    (click)="onSwitchVersion(replyId)">
                    {{ i + 1 }}
                  </button>
                }

                <!-- 下拉菜单显示其余版本 -->
                <button class="version-button more-versions" [matMenuTriggerFor]="versionMenu">
                  +{{ replyToMessage.replyIds.length - 2 }}
                </button>
                <mat-menu #versionMenu="matMenu">
                  @for (replyId of getRemainingVersions(2); track replyId; let i = $index) {
                    <button mat-menu-item
                            [disabled]="replyId === replyToMessage.activeReplyId"
                            (click)="onSwitchVersion(replyId)">
                      版本 {{ i + 3 }} {{ replyId === replyToMessage.activeReplyId ? '(当前)' : '' }}
                    </button>
                  }
                </mat-menu>
              }
            </div>
          }
        </div>
        <div class="message-body" [class.generating]="message.isGenerating">
          @if (isAssistant() && message.isGenerating) {
            <!-- 助手消息生成时使用打字效果 -->
            <app-typing-markdown
              [content]="message.content"
              [messageId]="message.id"
              [enableTyping]="true"
              [typingSpeed]="30">
            </app-typing-markdown>
          } @else {
            <!-- 其他情况使用普通markdown -->
            <app-markdown [content]="message.content"></app-markdown>
          }

          <!-- 附件列表 -->
          @if (message.attachments && message.attachments.length > 0) {
            <div class="message-attachments">
              @for (attachment of message.attachments; track attachment.id) {
                <a
                  class="attachment-item"
                  [href]="attachment.url"
                  target="_blank"
                  [matTooltip]="attachment.name"
                >
                  <div class="attachment-icon">
                    <!-- 根据文件类型显示不同图标 -->
                    @if (isImageFile(attachment.type)) {
                      <img [src]="attachment.url" alt="附件预览" class="attachment-preview">
                    } @else {
                      <div class="file-icon">
                        {{ getFileExtension(attachment.name).toUpperCase() }}
                      </div>
                    }
                  </div>
                  <div class="attachment-info">
                    <span class="attachment-name">{{ attachment.name }}</span>
                    <span class="attachment-size">{{ formatFileSize(attachment.size) }}</span>
                  </div>
                </a>
              }
            </div>
          }

          <!-- 助理消息操作按钮 -->
          <div class="message-actions assistant-actions">
            <button mat-icon-button matTooltip="复制" (click)="onCopy()">
              <mat-icon svgIcon="content_copy"></mat-icon>
            </button>
            <button mat-icon-button matTooltip="点赞" (click)="onLike()" [class.active]="message.liked">
              <mat-icon svgIcon="thumb_up"></mat-icon>
            </button>
            <button mat-icon-button matTooltip="点踩" (click)="onDislike()" [class.active]="message.disliked">
              <mat-icon svgIcon="thumb_down"></mat-icon>
            </button>
            <!-- 只有当助理消息有回复对象时才显示"重新生成"按钮 -->
            @if (message.replyToId) {
              <button mat-icon-button matTooltip="重新生成" (click)="onRegenerate()">
                <mat-icon svgIcon="refresh"></mat-icon>
              </button>
            }
          </div>
        </div>
      </div>
    }
  </div>
</div>
