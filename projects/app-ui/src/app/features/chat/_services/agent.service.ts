import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { Agent, UserAgentSubscription, SessionAgent, AgentMatchResult } from './execution-plan';

/**
 * Agent服务
 * 管理AI助手Agent的订阅、会话关联等功能
 */
@Injectable({
  providedIn: 'root'
})
export class AgentService {
  private readonly baseUrl = '/api/agents';
  
  // 当前用户订阅的Agent列表
  private subscribedAgentsSubject = new BehaviorSubject<Agent[]>([]);
  public subscribedAgents$ = this.subscribedAgentsSubject.asObservable();
  
  // 当前会话的Agent列表
  private sessionAgentsSubject = new BehaviorSubject<Agent[]>([]);
  public sessionAgents$ = this.sessionAgentsSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * 获取所有可用的Agent（包含订阅状态）
   */
  getAllAgents(): Observable<Agent[]> {
    return this.http.get<Agent[]>(this.baseUrl);
  }

  /**
   * 根据ID获取Agent
   */
  getAgentById(id: string): Observable<Agent> {
    return this.http.get<Agent>(`${this.baseUrl}/${id}`);
  }

  /**
   * 订阅Agent
   */
  subscribeAgent(agentId: string): Observable<any> {
    return this.http.post(`${this.baseUrl}/${agentId}/subscribe`, {}).pipe(
      tap(() => this.refreshSubscribedAgents())
    );
  }

  /**
   * 取消订阅Agent
   */
  unsubscribeAgent(agentId: string): Observable<any> {
    return this.http.post(`${this.baseUrl}/${agentId}/unsubscribe`, {}).pipe(
      tap(() => this.refreshSubscribedAgents())
    );
  }

  /**
   * 获取用户订阅的Agent列表
   */
  getUserSubscriptions(): Observable<Agent[]> {
    return this.http.get<Agent[]>(`${this.baseUrl}/subscriptions`).pipe(
      tap(agents => this.subscribedAgentsSubject.next(agents))
    );
  }

  /**
   * 为会话添加Agent
   */
  addAgentToSession(sessionId: string, agentId: string): Observable<any> {
    return this.http.post(`${this.baseUrl}/sessions/${sessionId}/agents/${agentId}`, {}).pipe(
      tap(() => this.refreshSessionAgents(sessionId))
    );
  }

  /**
   * 从会话中移除Agent
   */
  removeAgentFromSession(sessionId: string, agentId: string): Observable<any> {
    return this.http.delete(`${this.baseUrl}/sessions/${sessionId}/agents/${agentId}`).pipe(
      tap(() => this.refreshSessionAgents(sessionId))
    );
  }

  /**
   * 获取会话中的Agent列表
   */
  getSessionAgents(sessionId: string): Observable<Agent[]> {
    return this.http.get<Agent[]>(`${this.baseUrl}/sessions/${sessionId}/agents`).pipe(
      tap(agents => this.sessionAgentsSubject.next(agents))
    );
  }

  /**
   * 切换Agent在会话中的激活状态
   */
  toggleAgentInSession(sessionId: string, agentId: string, isActive: boolean): Observable<any> {
    if (isActive) {
      return this.addAgentToSession(sessionId, agentId);
    } else {
      return this.removeAgentFromSession(sessionId, agentId);
    }
  }

  /**
   * 格式化价格显示
   */
  formatPrice(priceInCents: number): string {
    if (priceInCents === 0) {
      return '免费';
    }
    const yuan = priceInCents / 100;
    return `¥${yuan}/月`;
  }

  /**
   * 获取Agent头像URL
   */
  getAvatarUrl(agent: Agent): string {
    if (agent.avatarUrl) {
      return agent.avatarUrl;
    }
    
    // 使用默认头像或emoji作为后备
    const defaultAvatars: { [key: string]: string } = {
      '通用助手': '🤖',
      '日程助理': '📅',
      '美工': '🎨',
      '活动策划': '🎉',
      '商情搜集': '📊'
    };
    
    return defaultAvatars[agent.name] || '🤖';
  }

  /**
   * 检查Agent是否可用（已订阅且未过期）
   */
  isAgentAvailable(agent: Agent): boolean {
    return agent.isDefault || agent.isSubscribed;
  }

  /**
   * 获取订阅状态文本
   */
  getSubscriptionStatusText(agent: Agent): string {
    if (agent.isDefault) {
      return '免费使用';
    }
    
    switch (agent.subscriptionStatus) {
      case 'SUBSCRIBED':
        return '已订阅';
      case 'EXPIRED':
        return '已过期';
      case 'NOT_SUBSCRIBED':
      default:
        return '未订阅';
    }
  }

  /**
   * 获取订阅状态颜色
   */
  getSubscriptionStatusColor(agent: Agent): string {
    if (agent.isDefault) {
      return 'primary';
    }
    
    switch (agent.subscriptionStatus) {
      case 'SUBSCRIBED':
        return 'primary';
      case 'EXPIRED':
        return 'warn';
      case 'NOT_SUBSCRIBED':
      default:
        return 'accent';
    }
  }

  /**
   * 刷新用户订阅的Agent列表
   */
  private refreshSubscribedAgents(): void {
    this.getUserSubscriptions().subscribe();
  }

  /**
   * 刷新会话Agent列表
   */
  private refreshSessionAgents(sessionId: string): void {
    this.getSessionAgents(sessionId).subscribe();
  }

  /**
   * 初始化用户数据
   */
  initializeUserData(): void {
    this.refreshSubscribedAgents();
  }

  /**
   * 初始化会话数据
   */
  initializeSessionData(sessionId: string): void {
    this.refreshSessionAgents(sessionId);
  }

  /**
   * 清理会话数据
   */
  clearSessionData(): void {
    this.sessionAgentsSubject.next([]);
  }

  /**
   * 获取当前订阅的Agent列表（同步）
   */
  getCurrentSubscribedAgents(): Agent[] {
    return this.subscribedAgentsSubject.value;
  }

  /**
   * 获取当前会话的Agent列表（同步）
   */
  getCurrentSessionAgents(): Agent[] {
    return this.sessionAgentsSubject.value;
  }

  /**
   * 检查是否有多个Agent在会话中激活
   */
  hasMultipleActiveAgents(): boolean {
    return this.getCurrentSessionAgents().length > 1;
  }

  /**
   * 获取会话中的主要Agent（第一个或唯一的）
   */
  getPrimarySessionAgent(): Agent | null {
    const agents = this.getCurrentSessionAgents();
    return agents.length > 0 ? agents[0] : null;
  }
}
