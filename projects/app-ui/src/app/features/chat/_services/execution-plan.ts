/**
 * Agent接口
 */
export interface Agent {
  id: string;
  name: string;
  description?: string;
  avatarUrl?: string;
  isActive: boolean;
  confidenceThreshold: number;
  priorityOrder: number;
  monthlyPrice: number; // 月订阅价格（分）
  isDefault: boolean; // 是否为默认Agent（免费）
  isSubscribed: boolean; // 用户是否已订阅
  subscriptionStatus: SubscriptionStatus;
  executionPlans: ExecutionPlan[];
}

/**
 * 执行计划接口
 */
export interface ExecutionPlan {
  id: string;
  name: string;
  description?: string;
  agent: Agent;
  isActive: boolean;
  priorityOrder: number;
  steps: ExecutionPlanStep[];
}

/**
 * 订阅状态枚举
 */
export type SubscriptionStatus = 'NOT_SUBSCRIBED' | 'SUBSCRIBED' | 'EXPIRED';

/**
 * 执行计划步骤接口
 */
export interface ExecutionPlanStep {
  id: string;
  name: string;
  description?: string;
  stepOrder: number;
  modelName?: string;
  expectedDurationSeconds?: number;
  isParallel: boolean;
}

/**
 * 消息执行上下文接口
 */
export interface MessageExecutionContext {
  id: string;
  messageId: string;
  executionPlan: ExecutionPlan;
  detectedDomain: string;
  confidenceScore: number;
  status: ExecutionStatus;
  currentStepOrder: number;
  startedAt?: Date;
  completedAt?: Date;
  stepExecutions: StepExecution[];
}

/**
 * 步骤执行记录接口
 */
export interface StepExecution {
  id: string;
  executionPlanStep: ExecutionPlanStep;
  status: StepStatus;
  startedAt?: Date;
  completedAt?: Date;
  durationSeconds?: number;
  output?: string;
  errorMessage?: string;
}

/**
 * 执行状态枚举
 */
export type ExecutionStatus = 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED' | 'CANCELLED';

/**
 * 步骤状态枚举
 */
export type StepStatus = 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';

/**
 * Agent匹配结果接口
 */
export interface AgentMatchResult {
  matchedAgents: Agent[]; // 匹配的Agent列表
  allAgents: AgentScore[]; // 所有Agent的评分
  hasConfidentMatch: boolean; // 是否有高置信度匹配
  reasoning?: string; // 匹配原因
}

/**
 * Agent评分接口
 */
export interface AgentScore {
  agent: Agent;
  similarity: number;
  isActive: boolean; // 在当前会话中是否激活
}

/**
 * 执行计划选择结果接口
 */
export interface ExecutionPlanSelectionResult {
  availablePlans: ExecutionPlan[]; // 可用的执行计划
  recommendedPlan?: ExecutionPlan; // 推荐的执行计划
  confidenceScore: number;
  isConfident: boolean; // 是否达到置信度阈值
  reasoning?: string; // 推荐原因
}

/**
 * 用户Agent订阅接口
 */
export interface UserAgentSubscription {
  id: string;
  agent: Agent;
  isActive: boolean;
  subscribedAt: Date;
  expiresAt?: Date;
  status: SubscriptionStatus;
}

/**
 * 会话Agent接口
 */
export interface SessionAgent {
  id: string;
  agent: Agent;
  isActive: boolean; // 是否在当前会话中激活
  addedAt: Date;
}
