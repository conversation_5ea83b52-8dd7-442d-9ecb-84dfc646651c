package biz.zhizuo.ai.assistant.service

import biz.zhizuo.ai.assistant.entity.*
import biz.zhizuo.ai.assistant.repository.ExecutionPlanRepository
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever
import org.springframework.ai.embedding.EmbeddingModel
import org.springframework.ai.embedding.EmbeddingRequest
import org.springframework.ai.embedding.EmbeddingResponse
import org.springframework.ai.embedding.Embedding
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

@ExtendWith(MockitoExtension::class)
class DomainServiceTest {

    @Mock
    private lateinit var domainRepository: DomainRepository

    @Mock
    private lateinit var executionPlanRepository: ExecutionPlanRepository

    @Mock
    private lateinit var embeddingModel: EmbeddingModel

    @InjectMocks
    private lateinit var domainService: DomainService

    @Test
    fun `should match domain with high confidence`() {
        // 准备测试数据
        val domain = Domain(
            id = "domain-technical",
            name = "技术问题",
            description = "处理技术相关问题",
            embeddingVector = floatArrayOf(0.1f, 0.2f, 0.3f),
            confidenceThreshold = 0.7
        )

        val userMessage = ChatMessage(
            session = createMockSession(),
            user = createMockUser(),
            role = MessageRole.USER,
            content = "如何解决Java内存溢出问题？"
        )

        // Mock 嵌入模型
        val mockEmbedding = Embedding(floatArrayOf(0.1f, 0.2f, 0.3f), 0)
        val mockResponse = EmbeddingResponse(listOf(mockEmbedding))
        whenever(embeddingModel.call(any<EmbeddingRequest>())).thenReturn(mockResponse)

        // Mock 仓库
        whenever(domainRepository.findAllActiveWithEmbedding()).thenReturn(listOf(domain))
        whenever(domainRepository.findById("domain-technical")).thenReturn(java.util.Optional.of(domain))

        // 手动设置向量缓存（模拟初始化）
        domainService.domainVectorCache["domain-technical"] = floatArrayOf(0.1f, 0.2f, 0.3f)

        // 执行测试
        val result = domainService.matchDomain(userMessage, emptyList())

        // 验证结果
        assertNotNull(result.domain)
        assertEquals("技术问题", result.domain!!.name)
        assertTrue(result.isConfident)
        assertTrue(result.confidence > 0.7)
    }

    @Test
    fun `should return uncertain result when confidence is low`() {
        // 准备测试数据
        val domain = Domain(
            id = "domain-technical",
            name = "技术问题",
            description = "处理技术相关问题",
            embeddingVector = floatArrayOf(0.1f, 0.2f, 0.3f),
            confidenceThreshold = 0.9 // 设置很高的阈值
        )

        val userMessage = ChatMessage(
            session = createMockSession(),
            user = createMockUser(),
            role = MessageRole.USER,
            content = "今天天气怎么样？" // 与技术领域不相关的问题
        )

        // Mock 嵌入模型返回不相似的向量
        val mockEmbedding = Embedding(floatArrayOf(0.9f, 0.8f, 0.7f), 0)
        val mockResponse = EmbeddingResponse(listOf(mockEmbedding))
        whenever(embeddingModel.call(any<EmbeddingRequest>())).thenReturn(mockResponse)

        // Mock 仓库
        whenever(domainRepository.findAllActiveWithEmbedding()).thenReturn(listOf(domain))

        // 手动设置向量缓存
        domainService.domainVectorCache["domain-technical"] = floatArrayOf(0.1f, 0.2f, 0.3f)

        // 执行测试
        val result = domainService.matchDomain(userMessage, emptyList())

        // 验证结果
        assertTrue(!result.isConfident) // 应该不确定
        assertTrue(result.confidence < 0.9) // 置信度应该低于阈值
    }

    private fun createMockSession(): ChatSession {
        return ChatSession(
            id = "session-1",
            user = createMockUser(),
            title = "测试会话"
        )
    }

    private fun createMockUser(): User {
        return User(
            id = "user-1",
            username = "testuser",
            email = "<EMAIL>",
            password = "password"
        )
    }
}

// 扩展函数来支持 any() 匹配器
private fun <T> any(): T {
    org.mockito.kotlin.any<T>()
    return null as T
}
