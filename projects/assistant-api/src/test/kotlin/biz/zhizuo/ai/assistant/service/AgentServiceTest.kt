package biz.zhizuo.ai.assistant.service

import biz.zhizuo.ai.assistant.entity.*
import biz.zhizuo.ai.assistant.repository.AgentRepository
import biz.zhizuo.ai.assistant.repository.ExecutionPlanRepository
import biz.zhizuo.ai.assistant.repository.UserAgentSubscriptionRepository
import biz.zhizuo.ai.assistant.repository.SessionAgentRepository
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever
import org.springframework.ai.embedding.EmbeddingModel
import org.springframework.ai.embedding.EmbeddingRequest
import org.springframework.ai.embedding.EmbeddingResponse
import org.springframework.ai.embedding.Embedding
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

@ExtendWith(MockitoExtension::class)
class AgentServiceTest {

    @Mock
    private lateinit var agentRepository: AgentRepository

    @Mock
    private lateinit var executionPlanRepository: ExecutionPlanRepository

    @Mock
    private lateinit var userAgentSubscriptionRepository: UserAgentSubscriptionRepository

    @Mock
    private lateinit var sessionAgentRepository: SessionAgentRepository

    @Mock
    private lateinit var embeddingModel: EmbeddingModel

    @InjectMocks
    private lateinit var agentService: AgentService

    @Test
    fun `should match agents with high confidence`() {
        // 准备测试数据
        val scheduleAgent = Agent(
            id = "agent-schedule",
            name = "日程助理",
            description = "专业的日程管理助手",
            embeddingVector = floatArrayOf(0.1f, 0.2f, 0.3f),
            confidenceThreshold = 0.7,
            monthlyPrice = 1000,
            isDefault = false
        )

        val generalAgent = Agent(
            id = "agent-general",
            name = "通用助手",
            description = "通用AI助手",
            embeddingVector = floatArrayOf(0.9f, 0.8f, 0.7f),
            confidenceThreshold = 0.6,
            monthlyPrice = 0,
            isDefault = true
        )

        val userMessage = ChatMessage(
            session = createMockSession(),
            user = createMockUser(),
            role = MessageRole.USER,
            content = "帮我安排明天的会议日程"
        )

        // Mock 嵌入模型
        val mockEmbedding = Embedding(floatArrayOf(0.1f, 0.2f, 0.3f), 0)
        val mockResponse = EmbeddingResponse(listOf(mockEmbedding))
        whenever(embeddingModel.call(any<EmbeddingRequest>())).thenReturn(mockResponse)

        // 手动设置向量缓存（模拟初始化）
        agentService.agentVectorCache["agent-schedule"] = floatArrayOf(0.1f, 0.2f, 0.3f)
        agentService.agentVectorCache["agent-general"] = floatArrayOf(0.9f, 0.8f, 0.7f)

        // 执行测试
        val sessionAgents = listOf(scheduleAgent, generalAgent)
        val result = agentService.matchAgents(userMessage, emptyList(), sessionAgents)

        // 验证结果
        assertTrue(result.hasConfidentMatch)
        assertTrue(result.matchedAgents.isNotEmpty())
        assertEquals("日程助理", result.matchedAgents.first().name)
    }

    @Test
    fun `should subscribe agent successfully`() {
        // 准备测试数据
        val user = createMockUser()
        val agent = Agent(
            id = "agent-schedule",
            name = "日程助理",
            description = "专业的日程管理助手",
            monthlyPrice = 1000,
            isDefault = false
        )

        // Mock 仓库方法
        whenever(userAgentSubscriptionRepository.findByUserAndAgentAndIsActiveTrue(user, agent))
            .thenReturn(null)

        val savedSubscription = UserAgentSubscription(
            id = "sub-1",
            user = user,
            agent = agent,
            isActive = true,
            subscribedAt = LocalDateTime.now(),
            expiresAt = LocalDateTime.now().plusMonths(1)
        )

        whenever(userAgentSubscriptionRepository.save(any<UserAgentSubscription>()))
            .thenReturn(savedSubscription)

        // 执行测试
        val result = agentService.subscribeAgent(user, agent)

        // 验证结果
        assertNotNull(result)
        assertEquals(user, result.user)
        assertEquals(agent, result.agent)
        assertTrue(result.isActive)
    }

    @Test
    fun `should add agent to session successfully`() {
        // 准备测试数据
        val session = createMockSession()
        val agent = Agent(
            id = "agent-schedule",
            name = "日程助理",
            description = "专业的日程管理助手",
            monthlyPrice = 1000,
            isDefault = false
        )

        // Mock 仓库方法
        whenever(sessionAgentRepository.findBySessionAndAgent(session, agent))
            .thenReturn(null)

        val savedSessionAgent = SessionAgent(
            id = "sa-1",
            session = session,
            agent = agent,
            isActive = true,
            addedAt = LocalDateTime.now()
        )

        whenever(sessionAgentRepository.save(any<SessionAgent>()))
            .thenReturn(savedSessionAgent)

        // 执行测试
        val result = agentService.addAgentToSession(session, agent)

        // 验证结果
        assertNotNull(result)
        assertEquals(session, result.session)
        assertEquals(agent, result.agent)
        assertTrue(result.isActive)
    }

    private fun createMockSession(): ChatSession {
        return ChatSession(
            id = "session-1",
            user = createMockUser(),
            title = "测试会话"
        )
    }

    private fun createMockUser(): User {
        return User(
            id = "user-1",
            username = "testuser",
            email = "<EMAIL>",
            password = "password"
        )
    }
}

// 扩展函数来支持 any() 匹配器
private fun <T> any(): T {
    org.mockito.kotlin.any<T>()
    return null as T
}
