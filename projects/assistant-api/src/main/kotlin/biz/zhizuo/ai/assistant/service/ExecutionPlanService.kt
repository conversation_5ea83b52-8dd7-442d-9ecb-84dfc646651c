package biz.zhizuo.ai.assistant.service

import biz.zhizuo.ai.assistant.dto.*
import biz.zhizuo.ai.assistant.entity.*
import biz.zhizuo.ai.assistant.repository.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.slf4j.LoggerFactory
import org.springframework.ai.chat.client.ChatClient
import org.springframework.ai.chat.model.ChatModel
import org.springframework.ai.chat.prompt.Prompt
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

/**
 * 执行计划服务
 * 负责执行计划的管理和步骤执行
 */
@Service
@Transactional
class ExecutionPlanService(
    private val executionPlanRepository: ExecutionPlanRepository,
    private val executionPlanStepRepository: ExecutionPlanStepRepository,
    private val messageExecutionContextRepository: MessageExecutionContextRepository,
    private val stepExecutionRepository: StepExecutionRepository,
    private val chatModel: ChatModel,
    private val sseService: SseService
) {

    private val logger = LoggerFactory.getLogger(ExecutionPlanService::class.java)
    private val coroutineScope = CoroutineScope(Dispatchers.IO)

    /**
     * 开始执行计划
     */
    fun startExecution(executionContextId: String, userId: String) {
        coroutineScope.launch {
            executeStepsSequentially(executionContextId, userId)
        }
    }

    /**
     * 顺序执行所有步骤
     */
    private suspend fun executeStepsSequentially(executionContextId: String, userId: String) {
        val executionContext = messageExecutionContextRepository.findById(executionContextId)
            .orElseThrow { IllegalArgumentException("执行上下文不存在: $executionContextId") }

        val messageId = executionContext.message.id!!
        val sseKey = "$userId:$messageId"

        try {
            // 更新执行状态为进行中
            updateExecutionStatus(executionContext, ExecutionStatus.IN_PROGRESS)

            // 发送执行开始事件
            sendExecutionEvent(sseKey, "execution-started", executionContext)

            // 获取所有步骤
            val steps = executionPlanStepRepository.findByExecutionPlanOrderByStepOrderAsc(executionContext.executionPlan)

            // 为每个步骤创建执行记录
            val stepExecutions = steps.map { step ->
                StepExecution(
                    executionContext = executionContext,
                    executionPlanStep = step,
                    status = StepStatus.PENDING
                )
            }
            stepExecutionRepository.saveAll(stepExecutions)

            // 逐步执行
            for (stepExecution in stepExecutions) {
                executeStep(stepExecution, sseKey)

                // 更新当前步骤
                updateCurrentStep(executionContext, stepExecution.executionPlanStep.stepOrder)
            }

            // 执行完成
            updateExecutionStatus(executionContext, ExecutionStatus.COMPLETED)
            sendExecutionEvent(sseKey, "execution-completed", executionContext)

        } catch (e: Exception) {
            logger.error("执行计划失败: ${e.message}", e)
            updateExecutionStatus(executionContext, ExecutionStatus.FAILED)
            sendExecutionEvent(sseKey, "execution-failed", executionContext, e.message)
        }
    }

    /**
     * 执行单个步骤
     */
    private suspend fun executeStep(stepExecution: StepExecution, sseKey: String) {
        val step = stepExecution.executionPlanStep
        val startTime = LocalDateTime.now()

        logger.info("开始执行步骤: ${step.name}")

        try {
            // 更新步骤状态为进行中
            val updatedStepExecution = stepExecution.copy(
                status = StepStatus.IN_PROGRESS,
                startedAt = startTime
            )
            stepExecutionRepository.save(updatedStepExecution)

            // 发送步骤开始事件
            sendStepEvent(sseKey, "step-started", updatedStepExecution)

            // 模拟步骤执行时间
            val expectedDuration = step.expectedDurationSeconds ?: 3
            delay(expectedDuration * 1000L)

            // 执行步骤逻辑
            val output = executeStepLogic(step, stepExecution.executionContext)

            val endTime = LocalDateTime.now()
            val duration = ChronoUnit.SECONDS.between(startTime, endTime).toInt()

            // 更新步骤状态为完成
            val completedStepExecution = updatedStepExecution.copy(
                status = StepStatus.COMPLETED,
                completedAt = endTime,
                durationSeconds = duration,
                output = output
            )
            stepExecutionRepository.save(completedStepExecution)

            // 发送步骤完成事件
            sendStepEvent(sseKey, "step-completed", completedStepExecution)

            logger.info("步骤执行完成: ${step.name}, 耗时: ${duration}秒")

        } catch (e: Exception) {
            logger.error("步骤执行失败: ${step.name}, 错误: ${e.message}", e)

            val failedStepExecution = stepExecution.copy(
                status = StepStatus.FAILED,
                completedAt = LocalDateTime.now(),
                errorMessage = e.message
            )
            stepExecutionRepository.save(failedStepExecution)

            // 发送步骤失败事件
            sendStepEvent(sseKey, "step-failed", failedStepExecution)

            throw e
        }
    }

    /**
     * 执行步骤的具体逻辑
     */
    private fun executeStepLogic(step: ExecutionPlanStep, executionContext: MessageExecutionContext): String {
        return when (step.name) {
            "确定问题领域" -> "已确定问题领域为：${executionContext.detectedDomain}"
            "理解问题" -> analyzeUserQuestion(executionContext)
            "搜集资料" -> "正在搜集相关资料..."
            "资料验证" -> "正在验证资料的准确性..."
            "资料处理" -> "正在处理和整理资料..."
            "汇总生成" -> generateResponse(executionContext, step)
            "结果验证" -> "正在验证生成结果的质量..."
            else -> "正在执行步骤：${step.name}"
        }
    }

    /**
     * 分析用户问题
     */
    private fun analyzeUserQuestion(executionContext: MessageExecutionContext): String {
        val userMessage = executionContext.message
        return "已分析用户问题：「${userMessage.content}」，问题类型为${executionContext.detectedDomain}"
    }

    /**
     * 生成回复内容
     */
    private fun generateResponse(executionContext: MessageExecutionContext, step: ExecutionPlanStep): String {
        val userMessage = executionContext.message

        // 如果有自定义提示词模板，使用模板
        val prompt = step.promptTemplate?.replace("{user_message}", userMessage.content)
            ?: "请回答以下问题：${userMessage.content}"

        return try {
            val chatClient = ChatClient.builder(chatModel).build()
            val response = chatClient.prompt(Prompt(prompt)).call().content()
            response ?: "生成回复时出现问题"
        } catch (e: Exception) {
            logger.error("生成回复失败: ${e.message}", e)
            "抱歉，生成回复时遇到了问题：${e.message}"
        }
    }

    /**
     * 更新执行状态
     */
    private fun updateExecutionStatus(executionContext: MessageExecutionContext, status: ExecutionStatus) {
        val updatedContext = executionContext.copy(
            status = status,
            startedAt = if (status == ExecutionStatus.IN_PROGRESS) LocalDateTime.now() else executionContext.startedAt,
            completedAt = if (status in listOf(ExecutionStatus.COMPLETED, ExecutionStatus.FAILED))
                LocalDateTime.now() else null
        )
        messageExecutionContextRepository.save(updatedContext)
    }

    /**
     * 更新当前步骤
     */
    private fun updateCurrentStep(executionContext: MessageExecutionContext, stepOrder: Int) {
        val updatedContext = executionContext.copy(currentStepOrder = stepOrder)
        messageExecutionContextRepository.save(updatedContext)
    }

    /**
     * 发送执行事件
     */
    private fun sendExecutionEvent(
        sseKey: String,
        eventType: String,
        executionContext: MessageExecutionContext,
        message: String? = null
    ) {
        val event = ExecutionPlanUpdateDto(
            messageId = executionContext.message.id!!,
            executionContext = executionContext.toDto(),
            eventType = eventType,
            message = message
        )
        sseService.sendMessageEvent(sseKey, "execution-update", event)
    }

    /**
     * 发送步骤事件
     */
    private fun sendStepEvent(sseKey: String, eventType: String, stepExecution: StepExecution) {
        val event = ExecutionPlanUpdateDto(
            messageId = stepExecution.executionContext.message.id!!,
            executionContext = stepExecution.executionContext.toDto(),
            eventType = eventType,
            currentStep = stepExecution.toDto()
        )
        sseService.sendMessageEvent(sseKey, "execution-update", event)
    }

    /**
     * 实体转DTO扩展函数
     */
    private fun MessageExecutionContext.toDto(): MessageExecutionContextDto {
        return MessageExecutionContextDto(
            id = this.id,
            messageId = this.message.id!!,
            executionPlan = this.executionPlan.toDto(),
            detectedDomain = this.detectedDomain,
            confidenceScore = this.confidenceScore,
            status = this.status,
            currentStepOrder = this.currentStepOrder,
            startedAt = this.startedAt,
            completedAt = this.completedAt,
            stepExecutions = this.stepExecutions.map { it.toDto() }
        )
    }

    private fun ExecutionPlan.toDto(): ExecutionPlanDto {
        return ExecutionPlanDto(
            id = this.id,
            name = this.name,
            description = this.description,
            agent = this.agent.toDto(),
            isActive = this.isActive,
            priorityOrder = this.priorityOrder,
            steps = this.steps.map { it.toDto() }
        )
    }

    private fun ExecutionPlanStep.toDto(): ExecutionPlanStepDto {
        return ExecutionPlanStepDto(
            id = this.id,
            name = this.name,
            description = this.description,
            stepOrder = this.stepOrder,
            modelName = this.modelName,
            expectedDurationSeconds = this.expectedDurationSeconds,
            isParallel = this.isParallel
        )
    }

    private fun StepExecution.toDto(): StepExecutionDto {
        return StepExecutionDto(
            id = this.id,
            executionPlanStep = this.executionPlanStep.toDto(),
            status = this.status,
            startedAt = this.startedAt,
            completedAt = this.completedAt,
            durationSeconds = this.durationSeconds,
            output = this.output,
            errorMessage = this.errorMessage
        )
    }
}
