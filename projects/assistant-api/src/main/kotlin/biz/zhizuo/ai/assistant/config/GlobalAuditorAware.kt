package biz.zhizuo.ai.assistant.config

import biz.zhizuo.ai.assistant.entity.User
import org.springframework.data.domain.AuditorAware
import org.springframework.security.core.Authentication
import org.springframework.security.core.context.SecurityContextHolder
import java.util.*

class GlobalAuditorAware : AuditorAware<String> {
    override fun getCurrentAuditor(): Optional<String> {
        val authentication: Authentication? = SecurityContextHolder.getContext().authentication
        val user = authentication?.principal as User?
        return Optional.ofNullable(user?.id)
    }

}
