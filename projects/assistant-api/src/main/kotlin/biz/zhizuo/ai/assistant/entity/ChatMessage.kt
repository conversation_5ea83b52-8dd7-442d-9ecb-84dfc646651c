package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.hibernate.annotations.Comment
import org.springframework.data.jpa.domain.support.AuditingEntityListener

/**
 * 聊天消息实体
 */
@Entity
@Table()
@Comment("聊天消息实体")
@EntityListeners(AuditingEntityListener::class)
data class ChatMessage(
    @Id
    val id: String = randomId(),

    @ManyToOne
    @Comment("所属聊天会话")
    val session: ChatSession,

    @ManyToOne
    @Comment("消息发送者")
    val user: User,

    @Enumerated(EnumType.STRING)
    @Column()
    @Comment("消息角色：用户、助手或系统")
    val role: MessageRole,

    @Column(columnDefinition = "TEXT")
    @Comment("消息内容")
    val content: String,

    @Column()
    @Comment("是否正在生成中")
    val isGenerating: Boolean = false,

    @ManyToOne
    @Comment("回复的目标消息")
    val replyTo: ChatMessage? = null,

    @OneToMany(mappedBy = "replyTo")
    @Comment("该消息的所有回复")
    val replies: List<ChatMessage> = emptyList(),

    @OneToOne
    @Comment("当前活跃的回复消息")
    var activeReply: ChatMessage? = null,

    @Comment("是否已归档")
    val isArchived: Boolean = false,

    @Column()
    @Comment("AI助手名称")
    val assistantName: String? = null,

    @Column()
    @Comment("是否被点赞")
    val liked: Boolean? = null,

    @Column()
    @Comment("是否被点踩")
    val disliked: Boolean? = null,

    @Embedded
    val auditInfo: AuditInfo = AuditInfo(),
    @OneToMany(mappedBy = "message", cascade = [CascadeType.ALL], fetch = FetchType.EAGER)
    @Comment("消息生成步骤列表")
    val steps: List<ChatMessageGenerationStep> = emptyList(),

    @OneToMany(mappedBy = "message", cascade = [CascadeType.ALL], fetch = FetchType.EAGER)
    @Comment("消息附件列表")
    val attachments: List<Attachment> = emptyList(),

    @OneToOne(mappedBy = "message", cascade = [CascadeType.ALL], fetch = FetchType.EAGER)
    @Comment("消息反馈信息")
    val feedback: ChatMessageFeedback? = null,
)

/**
 * 消息角色枚举
 */
enum class MessageRole {
    USER,
    ASSISTANT,
    SYSTEM,
}
