package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.config.FloatArrayConverter
import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.hibernate.annotations.Comment
import org.springframework.data.jpa.domain.support.AuditingEntityListener

/**
 * AI助手代理聚合根实体
 * 代表一个AI助手代理，如日程助理、美工、活动策划等
 */
@Entity
@Comment("AI助手代理聚合根实体")
@EntityListeners(AuditingEntityListener::class)
data class Agent(
    @Id
    val id: String = randomId(),

    @Column(unique = true)
    @Comment("给程序判断用的编码值")
    val code: String,

    @Column(length = 100, unique = true)
    @Comment("显示给用户看的 Agent 名称")
    val name: String,

    @Column(length = 4096)
    @Comment("Agent的详细描述信息")
    val description: String,

    @Column(length = 500)
    @Comment("Agent头像URL")
    val avatarUrl: String? = null,

    @Column(length = 65535)
    @Convert(converter = FloatArrayConverter::class)
    @Comment("Agent的嵌入向量，用于语义匹配")
    var embeddingVector: FloatArray? = null,

    @Comment("是否激活状态")
    val isActive: Boolean = true,

    @Comment("Agent匹配的置信度阈值")
    val confidenceThreshold: Double = 0.7,

    @Comment("优先级，数字越小优先级越高")
    val priorityOrder: Int = 0,

    @Comment("月订阅价格（分）")
    val monthlyPrice: Int = 0,

    @Comment("是否为默认Agent（免费）")
    val isDefault: Boolean = false,

    @Embedded
    val auditInfo: AuditInfo = AuditInfo(),

    @OneToMany(mappedBy = "agent", cascade = [CascadeType.ALL], fetch = FetchType.EAGER)
    val executionPlans: List<ExecutionPlan> = emptyList(),
)
