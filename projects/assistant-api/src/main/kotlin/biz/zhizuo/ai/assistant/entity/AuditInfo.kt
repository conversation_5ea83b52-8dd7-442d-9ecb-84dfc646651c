package biz.zhizuo.ai.assistant.entity

import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import org.hibernate.annotations.Comment
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedBy
import org.springframework.data.annotation.LastModifiedDate
import java.time.LocalDateTime

@Embeddable
class AuditInfo {
    @CreatedDate
    @Column(updatable = false)
    @Comment("创建时间")
    val createdAt: LocalDateTime = LocalDateTime.now()

    @CreatedBy
    @Column(updatable = false)
    @Comment("创建者")
    val createdById: String? = null

    @LastModifiedDate
    @Comment("最后更新时间")
    val lastModifiedAt: LocalDateTime? = null

    @LastModifiedBy
    @Comment("最后更新者")
    val lastModifiedById: String? = null
}
