package biz.zhizuo.ai.assistant.config

import com.fasterxml.jackson.databind.ObjectMapper
import jakarta.persistence.AttributeConverter
import jakarta.persistence.Converter
import kotlin.jvm.java
import kotlin.let

@Converter(autoApply = true) // 自动应用于 FloatArray 类型的属性
class FloatArrayConverter : AttributeConverter<FloatArray, String> {
    private val objectMapper = ObjectMapper()
    override fun convertToDatabaseColumn(attribute: FloatArray?): String? {
        return attribute?.let { objectMapper.writeValueAsString(it) }
    }

    override fun convertToEntityAttribute(dbData: String?): FloatArray? {
        return dbData?.let { objectMapper.readValue(it, FloatArray::class.java) }
    }
}
