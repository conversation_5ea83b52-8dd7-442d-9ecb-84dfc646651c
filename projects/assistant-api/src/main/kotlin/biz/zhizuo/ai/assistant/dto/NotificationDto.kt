package biz.zhizuo.ai.assistant.dto

import biz.zhizuo.ai.assistant.entity.NotificationType
import java.time.LocalDateTime

/**
 * 消息 DTO
 */
data class NotificationDto(
    val id: String?,
    val type: NotificationType,
    val category: String?,
    val title: String,
    val content: String?,
    val isRead: Boolean,
    val icon: String?,
    val color: String?,
    val createdAt: LocalDateTime?,
)

/**
 * 消息响应 DTO
 */
data class NotificationResponse(
    val notifications: List<NotificationDto>,
    val privateMessages: List<NotificationDto>,
)
