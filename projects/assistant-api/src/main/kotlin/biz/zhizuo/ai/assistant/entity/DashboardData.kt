package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.hibernate.annotations.Comment
import org.springframework.data.jpa.domain.support.AuditingEntityListener

/**
 * 仪表盘数据实体
 */
@Entity
@Table()
@Comment("仪表盘数据实体")
@EntityListeners(AuditingEntityListener::class)
data class DashboardData(
    @Id
    val id: String = randomId(),

    @Column(length = 50)
    @Comment("数据类型：stat或activity")
    val type: String,

    @Column(length = 100)
    @Comment("数据标题")
    val title: String,

    @Column(columnDefinition = "TEXT")
    @Comment("数据描述")
    val description: String? = null,

    @Column()
    @Comment("数据值")
    val value: String,

    @Column(length = 50)
    @Comment("图标名称")
    val icon: String? = null,

    @Column(length = 50)
    @Comment("颜色值")
    val color: String? = null,

    @Column()
    @Comment("排序顺序")
    val sortOrder: Int = 0,

    @Embedded
    val auditInfo: AuditInfo = AuditInfo(),
)
