package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.hibernate.annotations.Comment
import org.springframework.data.jpa.domain.support.AuditingEntityListener

/**
 * 通知实体
 */
@Entity
@Table()
@Comment("通知实体")
@EntityListeners(AuditingEntityListener::class)
data class Notification(
    @Id
    val id: String = randomId(),

    @Column()
    @Comment("接收通知的用户ID，为空表示全局通知")
    val userId: String? = null,

    @Column(length = 50)
    @Comment("通知类型")
    val type: NotificationType,

    @Column(length = 50)
    @Comment("通知分类")
    val category: String? = null,

    @Column(length = 200)
    @Comment("通知标题")
    val title: String,

    @Column(columnDefinition = "TEXT")
    @Comment("通知内容")
    val content: String? = null,

    @Column()
    @Comment("是否已读")
    val isRead: Boolean = false,

    @Column(length = 50)
    @Comment("图标名称")
    val icon: String? = null,

    @Column(length = 50)
    @Comment("颜色值")
    val color: String? = null,

    @Embedded
    val auditInfo: AuditInfo = AuditInfo(),
)
