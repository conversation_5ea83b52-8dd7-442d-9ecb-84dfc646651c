package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.hibernate.annotations.Comment
import org.springframework.data.jpa.domain.support.AuditingEntityListener

/**
 * 建议反馈实体
 * 记录用户选择建议的数据，用于分析建议有效性和改进未来推荐
 */
@Entity
@Table()
@Comment("建议反馈实体")
@EntityListeners(AuditingEntityListener::class)
data class SuggestionFeedback(
    @Id
    var id: String = randomId(),

    @Column(columnDefinition = "TEXT")
    @Comment("建议文本内容")
    var suggestionText: String,

    @Column()
    @Comment("是否为报告模板")
    var isReportTemplate: Boolean = false,

    @Column()
    @Comment("关联的消息ID")
    var messageId: String,

    @Column()
    @Comment("关联的会话ID")
    var sessionId: String,

    @Column()
    @Comment("建议ID")
    var suggestionId: String,

    @Column()
    @Comment("关联的用户ID，可以为空（如果建议是在非登录状态下提供和点击的）")
    var userId: String? = null,

    @Embedded
    val auditInfo: AuditInfo = AuditInfo(),
)
