package biz.zhizuo.ai.assistant.service

import biz.zhizuo.ai.assistant.entity.*
import biz.zhizuo.ai.assistant.repository.AgentRepository
import biz.zhizuo.ai.assistant.repository.ExecutionPlanRepository
import biz.zhizuo.ai.assistant.repository.SessionAgentRepository
import biz.zhizuo.ai.assistant.repository.UserAgentSubscriptionRepository
import jakarta.annotation.PostConstruct
import org.slf4j.LoggerFactory
import org.springframework.ai.embedding.EmbeddingModel
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.sqrt

/**
 * Agent服务
 * 负责管理Agent的向量匹配、订阅管理和Agent识别
 */
@Service
@Transactional
class AgentService(
    private val agentRepository: AgentRepository,
    private val executionPlanRepository: ExecutionPlanRepository,
    private val userAgentSubscriptionRepository: UserAgentSubscriptionRepository,
    private val sessionAgentRepository: SessionAgentRepository,
    private val embeddingModel: EmbeddingModel,
) {

    private val logger = LoggerFactory.getLogger(AgentService::class.java)

    // 内存中的Agent向量缓存
    internal val agentVectorCache = ConcurrentHashMap<String, FloatArray>()

    // 内存中的执行计划向量缓存
    internal val executionPlanVectorCache = ConcurrentHashMap<String, FloatArray>()

    // 默认置信度阈值
    private val defaultConfidenceThreshold = 0.7

    /**
     * 初始化时加载所有Agent和执行计划的向量到内存
     */
    @PostConstruct
    fun initializeVectorCache() {
        logger.info("开始初始化向量缓存...")

        try {
            // 加载所有活跃的Agent向量
            val activeAgents = agentRepository.findAllActiveWithEmbedding()
            activeAgents.forEach { agent ->
                agent.embeddingVector?.let { vector ->
                    agentVectorCache[agent.id!!] = vector
                    logger.debug("加载Agent向量: ${agent.name}")
                }
            }

            // 加载所有活跃的执行计划向量
            val activeExecutionPlans = executionPlanRepository.findByIsActiveTrueOrderByPriorityOrderAsc()
            activeExecutionPlans.forEach { plan ->
                plan.embeddingVector?.let { vector ->
                    executionPlanVectorCache[plan.id!!] = vector
                    logger.debug("加载执行计划向量: ${plan.name}")
                }
            }

            logger.info("向量缓存初始化完成，Agent数量: ${agentVectorCache.size}, 执行计划数量: ${executionPlanVectorCache.size}")
        } catch (e: Exception) {
            logger.error("向量缓存初始化失败", e)
        }
    }

    /**
     * 刷新向量缓存
     */
    fun refreshVectorCache() {
        agentVectorCache.clear()
        executionPlanVectorCache.clear()
        initializeVectorCache()
    }

    /**
     * 根据用户消息和会话中的Agent匹配最合适的Agent
     */
    fun matchAgents(
        userMessage: ChatMessage,
        sessionHistory: List<ChatMessage>,
        sessionAgents: List<Agent>,
    ): AgentMatchResult {
        logger.info("开始为消息 ${userMessage.id} 匹配Agent")

        // 生成查询向量
        val queryVector = generateQueryVector(userMessage, sessionHistory)

        if (queryVector.isEmpty()) {
            logger.warn("无法生成查询向量，使用默认Agent")
            return getDefaultAgentResult(sessionAgents)
        }

        // 在会话Agent中进行向量相似度计算
        val agentScores = mutableListOf<AgentScore>()

        sessionAgents.forEach { agent ->
            agentVectorCache[agent.id!!]?.let { agentVector ->
                val similarity = calculateCosineSimilarity(queryVector, agentVector)
                agentScores.add(AgentScore(agent, similarity))
            }
        }

        // 按相似度排序
        agentScores.sortByDescending { it.similarity }

        if (agentScores.isEmpty()) {
            logger.warn("没有找到匹配的Agent，使用默认Agent")
            return getDefaultAgentResult(sessionAgents)
        }

        // 找出高置信度的Agent
        val confidentAgents = agentScores.filter { it.similarity >= it.agent.confidenceThreshold }

        logger.info("匹配到 ${confidentAgents.size} 个高置信度Agent")

        return AgentMatchResult(
            matchedAgents = confidentAgents.map { it.agent },
            allScores = agentScores.map { AgentScore(it.agent, it.similarity) },
            hasConfidentMatch = confidentAgents.isNotEmpty()
        )
    }

    /**
     * 根据Agent匹配最合适的执行计划
     */
    fun matchExecutionPlans(
        agent: Agent,
        userMessage: ChatMessage,
        sessionHistory: List<ChatMessage>,
    ): ExecutionPlanMatchResult {
        logger.info("开始为Agent ${agent.name} 匹配执行计划")

        // 生成查询向量
        val queryVector = generateQueryVector(userMessage, sessionHistory)

        if (queryVector.isEmpty()) {
            logger.warn("无法生成查询向量，使用默认执行计划")
            return getDefaultExecutionPlanResult(agent)
        }

        // 获取该Agent下的所有执行计划
        val agentExecutionPlans = executionPlanRepository.findActiveWithEmbeddingByAgent(agent)

        if (agentExecutionPlans.isEmpty()) {
            logger.warn("Agent ${agent.name} 下没有可用的执行计划")
            return ExecutionPlanMatchResult(
                executionPlans = emptyList(),
                confidence = 0.0,
                isConfident = false
            )
        }

        // 计算相似度
        val planScores = mutableListOf<ExecutionPlanScore>()

        agentExecutionPlans.forEach { plan ->
            plan.embeddingVector?.let { planVector ->
                val similarity = calculateCosineSimilarity(queryVector, planVector)
                planScores.add(ExecutionPlanScore(plan, similarity))
            }
        }

        // 按相似度排序
        planScores.sortByDescending { it.similarity }

        if (planScores.isEmpty()) {
            return getDefaultExecutionPlanResult(agent)
        }

        val bestMatch = planScores.first()
        val isConfident = bestMatch.similarity >= defaultConfidenceThreshold

        logger.info("最佳匹配执行计划: ${bestMatch.executionPlan.name}, 相似度: ${bestMatch.similarity}")

        return ExecutionPlanMatchResult(
            executionPlans = planScores.map { it.executionPlan },
            confidence = bestMatch.similarity,
            isConfident = isConfident
        )
    }

    /**
     * 生成查询向量
     */
    private fun generateQueryVector(userMessage: ChatMessage, sessionHistory: List<ChatMessage>): FloatArray {
        // 构建查询文本：当前消息 + 最近的会话历史
        val queryText = buildString {
            append(userMessage.content)

            // 添加最近3条历史消息作为上下文
            sessionHistory.takeLast(3).forEach { msg ->
                append(" ")
                append(msg.content)
            }
        }

        return embeddingModel.embed(queryText)
    }

    /**
     * 计算余弦相似度
     */
    private fun calculateCosineSimilarity(vector1: FloatArray, vector2: FloatArray): Double {
        if (vector1.size != vector2.size) {
            return 0.0
        }

        var dotProduct = 0.0
        var norm1 = 0.0
        var norm2 = 0.0

        for (i in vector1.indices) {
            dotProduct += vector1[i] * vector2[i]
            norm1 += vector1[i] * vector1[i]
            norm2 += vector2[i] * vector2[i]
        }

        return if (norm1 == 0.0 || norm2 == 0.0) {
            0.0
        } else {
            dotProduct / (sqrt(norm1) * sqrt(norm2))
        }
    }

    /**
     * 获取用户订阅的Agent列表
     */
    fun getUserSubscribedAgents(user: User): List<Agent> {
        val subscriptions = userAgentSubscriptionRepository.findByUserAndIsActiveTrueOrderByCreatedAtDesc(user)
        return subscriptions.filter { isSubscriptionValid(it) }.map { it.agent }
    }

    /**
     * 订阅Agent
     */
    fun subscribeAgent(user: User, agent: Agent): UserAgentSubscription {
        // 检查是否已经订阅
        val existingSubscription = userAgentSubscriptionRepository.findByUserAndAgentAndIsActiveTrue(user, agent)
        if (existingSubscription != null) {
            throw IllegalStateException("用户已经订阅了该Agent")
        }

        val expiresAt = if (agent.isDefault) null else LocalDateTime.now().plusMonths(1)

        val subscription = UserAgentSubscription(
            user = user,
            agent = agent,
            isActive = true,
            subscribedAt = LocalDateTime.now(),
            expiresAt = expiresAt
        )

        return userAgentSubscriptionRepository.save(subscription)
    }

    /**
     * 取消订阅Agent
     */
    fun unsubscribeAgent(user: User, agent: Agent): Boolean {
        val subscription = userAgentSubscriptionRepository.findByUserAndAgentAndIsActiveTrue(user, agent)
        return if (subscription != null) {
            val updatedSubscription = subscription.copy(isActive = false)
            userAgentSubscriptionRepository.save(updatedSubscription)
            true
        } else {
            false
        }
    }

    /**
     * 为会话添加Agent
     */
    fun addAgentToSession(session: ChatSession, agent: Agent): SessionAgent {
        // 检查是否已经添加
        val existingSessionAgent = sessionAgentRepository.findBySessionAndAgent(session, agent)
        if (existingSessionAgent != null) {
            // 如果已存在但未激活，则激活它
            if (!existingSessionAgent.isActive) {
                val updatedSessionAgent = existingSessionAgent.copy(isActive = true)
                return sessionAgentRepository.save(updatedSessionAgent)
            }
            return existingSessionAgent
        }

        val sessionAgent = SessionAgent(
            session = session,
            agent = agent,
            isActive = true,
            addedAt = LocalDateTime.now()
        )

        return sessionAgentRepository.save(sessionAgent)
    }

    /**
     * 从会话中移除Agent（设为非激活状态）
     */
    fun removeAgentFromSession(session: ChatSession, agent: Agent): Boolean {
        val sessionAgent = sessionAgentRepository.findBySessionAndAgent(session, agent)
        return if (sessionAgent != null && sessionAgent.isActive) {
            val updatedSessionAgent = sessionAgent.copy(isActive = false)
            sessionAgentRepository.save(updatedSessionAgent)
            true
        } else {
            false
        }
    }

    /**
     * 获取会话中的活跃Agent
     */
    fun getSessionActiveAgents(session: ChatSession): List<Agent> {
        val sessionAgents = sessionAgentRepository.findBySessionAndIsActiveTrueOrderByAddedAtAsc(session)
        return sessionAgents.map { it.agent }
    }

    /**
     * 检查订阅是否有效
     */
    private fun isSubscriptionValid(subscription: UserAgentSubscription): Boolean {
        if (!subscription.isActive) return false
        return subscription.expiresAt?.isAfter(LocalDateTime.now()) ?: true
    }

    /**
     * 获取默认Agent结果
     */
    private fun getDefaultAgentResult(sessionAgents: List<Agent>): AgentMatchResult {
        return AgentMatchResult(
            matchedAgents = sessionAgents.take(1), // 默认选择第一个
            allScores = sessionAgents.map { AgentScore(it, 0.0) },
            hasConfidentMatch = false
        )
    }

    /**
     * 获取默认执行计划结果
     */
    private fun getDefaultExecutionPlanResult(agent: Agent): ExecutionPlanMatchResult {
        val defaultPlans = executionPlanRepository.findByAgentAndIsActiveTrueOrderByPriorityOrderAsc(agent)
        return ExecutionPlanMatchResult(
            executionPlans = defaultPlans,
            confidence = 0.0,
            isConfident = false
        )
    }
}

/**
 * Agent匹配结果
 */
data class AgentMatchResult(
    val matchedAgents: List<Agent>, // 匹配的Agent列表
    val allScores: List<AgentScore>, // 所有Agent的评分
    val hasConfidentMatch: Boolean, // 是否有高置信度匹配
)

/**
 * 执行计划匹配结果
 */
data class ExecutionPlanMatchResult(
    val executionPlans: List<ExecutionPlan>, // 匹配的执行计划列表
    val confidence: Double, // 匹配置信度
    val isConfident: Boolean, // 是否达到置信度阈值
)

/**
 * Agent评分
 */
data class AgentScore(
    val agent: Agent,
    val similarity: Double,
)

/**
 * 执行计划评分
 */
private data class ExecutionPlanScore(
    val executionPlan: ExecutionPlan,
    val similarity: Double,
)
