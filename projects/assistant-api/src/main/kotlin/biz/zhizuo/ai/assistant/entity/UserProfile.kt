package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.hibernate.annotations.Comment
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDate

/**
 * 用户资料实体
 */
@Entity
@Table()
@Comment("用户资料实体")
@EntityListeners(AuditingEntityListener::class)
data class UserProfile(
    @Id
    val id: String = randomId(),

    @Column(unique = true)
    @Comment("关联的用户ID")
    val userId: String,

    @Column(length = 100)
    @Comment("用户显示名称")
    val name: String,

    @Column(length = 255)
    @Comment("用户头像URL")
    val avatar: String? = null,

    @Column(length = 100)
    @Comment("用户邮箱")
    val email: String,

    @Column()
    @Comment("加入日期")
    val joinDate: LocalDate? = null,

    @Column(columnDefinition = "TEXT")
    @Comment("用户个人简介")
    val bio: String? = null,

    @Column()
    @Comment("发帖数量")
    val postsCount: Int = 0,

    @Column()
    @Comment("粉丝数量")
    val followersCount: Int = 0,

    @Column()
    @Comment("关注数量")
    val followingCount: Int = 0,

    @Embedded
    val auditInfo: AuditInfo = AuditInfo(),
)
