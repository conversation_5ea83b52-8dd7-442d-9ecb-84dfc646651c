package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.hibernate.annotations.Comment
import org.springframework.data.jpa.domain.support.AuditingEntityListener

/**
 * 聊天消息反馈实体
 */
@Entity
@Table()
@Comment("聊天消息反馈实体")
@EntityListeners(AuditingEntityListener::class)
data class ChatMessageFeedback(
    @Id
    val id: String = randomId(),

    @OneToOne
    @Comment("关联的消息")
    val message: ChatMessage,

    @Comment("评分")
    val rate: Int,

    @Column(columnDefinition = "TEXT")
    @Comment("反馈内容")
    val content: String? = null,

    @Embedded
    val auditInfo: AuditInfo = AuditInfo(),
)
