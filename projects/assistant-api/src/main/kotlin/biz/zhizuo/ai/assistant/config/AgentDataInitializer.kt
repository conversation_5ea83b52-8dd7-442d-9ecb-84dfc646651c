package biz.zhizuo.ai.assistant.config

import biz.zhizuo.ai.assistant.entity.Agent
import biz.zhizuo.ai.assistant.entity.ExecutionPlan
import biz.zhizuo.ai.assistant.entity.ExecutionPlanStep
import biz.zhizuo.ai.assistant.repository.AgentRepository
import biz.zhizuo.ai.assistant.repository.ExecutionPlanRepository
import biz.zhizuo.ai.assistant.repository.ExecutionPlanStepRepository
import org.slf4j.LoggerFactory
import org.springframework.boot.CommandLineRunner
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

/**
 * Agent数据初始化器
 * 在应用启动时创建默认的Agent和执行计划
 */
@Component
class AgentDataInitializer(
    private val agentRepository: AgentRepository,
    private val executionPlanRepository: ExecutionPlanRepository,
    private val executionPlanStepRepository: ExecutionPlanStepRepository
) : CommandLineRunner {

    private val logger = LoggerFactory.getLogger(AgentDataInitializer::class.java)

    @Transactional
    override fun run(vararg args: String?) {
        if (agentRepository.count() == 0L) {
            logger.info("开始初始化默认Agent数据...")
            initializeDefaultAgents()
            logger.info("默认Agent数据初始化完成")
        } else {
            logger.info("Agent数据已存在，跳过初始化")
        }
    }

    private fun initializeDefaultAgents() {
        // 1. 通用助手 - 免费
        val generalAgent = createAgent(
            id = "agent-general",
            code = "default",
            name = "通用助手",
            description = "处理一般性问题和日常咨询的智能助手，为您提供全方位的帮助",
            avatarUrl = "/assets/avatars/general-assistant.png",
            monthlyPrice = 0,
            isDefault = true,
            priorityOrder = 100
        )

        // 2. 日程助理 - 10元/月
        val scheduleAgent = createAgent(
            id = "agent-schedule",
            code = "schedule",
            name = "日程助理",
            description = "专业的日程管理和时间规划助手，帮您高效安排时间，提升工作效率",
            avatarUrl = "/assets/avatars/schedule-assistant.png",
            monthlyPrice = 1000, // 10元 = 1000分
            isDefault = false,
            priorityOrder = 10
        )

        // 3. 美工 - 200元/月
        val designerAgent = createAgent(
            id = "agent-designer",
            code = "designer",
            name = "美工",
            description = "专业的视觉设计师，提供创意设计和美术指导，让您的作品更加出色",
            avatarUrl = "/assets/avatars/designer.png",
            monthlyPrice = 20000, // 200元 = 20000分
            isDefault = false,
            priorityOrder = 20
        )

        // 4. 活动策划 - 200元/月
        val eventPlannerAgent = createAgent(
            id = "agent-event-planner",
            code = "event-planner",
            name = "活动策划",
            description = "经验丰富的活动策划专家，为您打造完美活动，从策划到执行全程指导",
            avatarUrl = "/assets/avatars/event-planner.png",
            monthlyPrice = 20000, // 200元 = 20000分
            isDefault = false,
            priorityOrder = 30
        )

        // 5. 商情搜集 - 1000元/月
        val marketResearcherAgent = createAgent(
            id = "agent-market-researcher",
            code = "market-researcher",
            name = "商情搜集",
            description = "专业的市场研究分析师，提供深度商业洞察和市场分析，助力商业决策",
            avatarUrl = "/assets/avatars/market-researcher.png",
            monthlyPrice = 100000, // 1000元 = 100000分
            isDefault = false,
            priorityOrder = 40
        )

        // 创建执行计划
        createExecutionPlansForAgents(
            generalAgent, scheduleAgent, designerAgent, 
            eventPlannerAgent, marketResearcherAgent
        )
    }

    private fun createAgent(
        id: String,
        code: String,
        name: String,
        description: String,
        avatarUrl: String,
        monthlyPrice: Int,
        isDefault: Boolean,
        priorityOrder: Int
    ): Agent {
        val agent = Agent(
            id = id,
            code = code,
            name = name,
            description = description,
            avatarUrl = avatarUrl,
            monthlyPrice = monthlyPrice,
            isDefault = isDefault,
            priorityOrder = priorityOrder,
            isActive = true,
            confidenceThreshold = 0.7
        )
        return agentRepository.save(agent)
    }

    private fun createExecutionPlansForAgents(vararg agents: Agent) {
        agents.forEach { agent ->
            when (agent.name) {
                "通用助手" -> createGeneralAgentPlans(agent)
                "日程助理" -> createScheduleAgentPlans(agent)
                "美工" -> createDesignerAgentPlans(agent)
                "活动策划" -> createEventPlannerAgentPlans(agent)
                "商情搜集" -> createMarketResearcherAgentPlans(agent)
            }
        }
    }

    private fun createGeneralAgentPlans(agent: Agent) {
        val plan = createExecutionPlan(
            id = "plan-general-chat",
            name = "通用对话",
            description = "处理日常对话和一般性问题",
            agent = agent,
            priorityOrder = 1
        )

        createExecutionPlanStep(plan, 1, "理解问题", "分析用户问题的意图和需求", 5)
        createExecutionPlanStep(plan, 2, "生成回复", "基于理解生成合适的回复", 10)
    }

    private fun createScheduleAgentPlans(agent: Agent) {
        val createPlan = createExecutionPlan(
            id = "plan-schedule-create",
            name = "创建日程",
            description = "帮助用户创建和安排日程",
            agent = agent,
            priorityOrder = 1
        )

        createExecutionPlanStep(createPlan, 1, "理解需求", "分析用户的日程安排需求", 5)
        createExecutionPlanStep(createPlan, 2, "制定计划", "制定详细的日程安排计划", 10)
        createExecutionPlanStep(createPlan, 3, "确认安排", "确认最终的日程安排", 5)

        val managePlan = createExecutionPlan(
            id = "plan-schedule-manage",
            name = "管理日程",
            description = "管理和调整现有日程安排",
            agent = agent,
            priorityOrder = 2
        )

        createExecutionPlanStep(managePlan, 1, "分析现状", "分析当前日程安排情况", 5)
        createExecutionPlanStep(managePlan, 2, "优化调整", "优化和调整日程安排", 10)
    }

    private fun createDesignerAgentPlans(agent: Agent) {
        val designPlan = createExecutionPlan(
            id = "plan-design-create",
            name = "创意设计",
            description = "进行创意设计和视觉创作",
            agent = agent,
            priorityOrder = 1
        )

        createExecutionPlanStep(designPlan, 1, "需求分析", "分析设计需求和目标", 10)
        createExecutionPlanStep(designPlan, 2, "概念设计", "创建设计概念和方案", 20)
        createExecutionPlanStep(designPlan, 3, "细化设计", "完善和细化设计作品", 15)
    }

    private fun createEventPlannerAgentPlans(agent: Agent) {
        val planningPlan = createExecutionPlan(
            id = "plan-event-planning",
            name = "活动策划",
            description = "制定完整的活动策划方案",
            agent = agent,
            priorityOrder = 1
        )

        createExecutionPlanStep(planningPlan, 1, "需求分析", "分析活动目标和要求", 15)
        createExecutionPlanStep(planningPlan, 2, "方案设计", "设计活动方案和流程", 25)
        createExecutionPlanStep(planningPlan, 3, "方案提交", "提交完整的活动策划方案", 10)
    }

    private fun createMarketResearcherAgentPlans(agent: Agent) {
        val researchPlan = createExecutionPlan(
            id = "plan-market-research",
            name = "市场调研",
            description = "进行深度市场调研分析",
            agent = agent,
            priorityOrder = 1
        )

        createExecutionPlanStep(researchPlan, 1, "确定范围", "确定调研范围和目标", 10)
        createExecutionPlanStep(researchPlan, 2, "数据收集", "收集相关市场数据", 30)
        createExecutionPlanStep(researchPlan, 3, "分析报告", "生成详细的分析报告", 20)
    }

    private fun createExecutionPlan(
        id: String,
        name: String,
        description: String,
        agent: Agent,
        priorityOrder: Int
    ): ExecutionPlan {
        val plan = ExecutionPlan(
            id = id,
            name = name,
            description = description,
            agent = agent,
            priorityOrder = priorityOrder,
            isActive = true
        )
        return executionPlanRepository.save(plan)
    }

    private fun createExecutionPlanStep(
        executionPlan: ExecutionPlan,
        stepOrder: Int,
        name: String,
        description: String,
        expectedDurationSeconds: Int
    ): ExecutionPlanStep {
        val step = ExecutionPlanStep(
            executionPlan = executionPlan,
            stepOrder = stepOrder,
            name = name,
            description = description,
            expectedDurationSeconds = expectedDurationSeconds,
            isParallel = false
        )
        return executionPlanStepRepository.save(step)
    }
}
