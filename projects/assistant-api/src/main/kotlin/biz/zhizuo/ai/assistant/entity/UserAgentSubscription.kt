package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.hibernate.annotations.Comment
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 用户Agent订阅实体
 */
@Entity
@Comment("用户Agent订阅实体")
@EntityListeners(AuditingEntityListener::class)
data class UserAgentSubscription(
    @Id
    val id: String = randomId(),

    @ManyToOne(fetch = FetchType.LAZY)
    @Comment("订阅用户")
    val user: User,

    @ManyToOne(fetch = FetchType.LAZY)
    @Comment("订阅的Agent")
    val agent: Agent,

    @Comment("订阅是否激活")
    val isActive: Boolean = true,

    @Comment("订阅时间")
    val subscribedAt: LocalDateTime = LocalDateTime.now(),

    @Comment("订阅到期时间，null表示永久")
    val expiresAt: LocalDateTime? = null,

    @Embedded
    val auditInfo: AuditInfo = AuditInfo(),
)
